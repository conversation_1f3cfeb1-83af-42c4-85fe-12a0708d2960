import React from 'react'
import ControlPanel from './ControlPanel'
import { Button } from './ui/button'
import { Printer } from 'lucide-react'
import { useChart } from '../contexts/ChartContext'
import { useState, useRef, useCallback,useEffect } from 'react'

// 声明 window.api 类型
declare global {
  interface Window {
    api: {
      [x: string]: any
      activation: unknown
      printView: (html: string) => Promise<void>
    }
    echartsInstance: {
      setOption: (options: {
        grid?: {
          left?: string
          right?: string
          top?: string
          bottom?: string
          containLabel?: boolean
        }
        toolbox?: {
          show?: boolean
        }
        dataZoom?: Array<{
          show?: boolean
        }>
      }) => void
      getDataURL: () => string
    }
  }
}

const SidePanel: React.FC = () => {
  const { state } = useChart()
  const { feature1Data, feature2Data, chartData } = state
  const [isPrinting, setIsPrinting] = useState(false)
  const printTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 打印功能
  const handlePrint = useCallback(() => {
    setIsPrinting(true)
    
    // 清理之前的定时器
    if (printTimerRef.current) {
      clearTimeout(printTimerRef.current)
    }
    
    // 隐藏工具箱和缩放控件
    if (window.echartsInstance) {
      window.echartsInstance.setOption({
        grid: {
          left: '5%',
          right: '5%',
          top: '5%',
          bottom: '0%',
          containLabel: true
        },
        toolbox: {
          show: false
        },
        dataZoom: [
          { show: false },
          { show: false },
          { show: false },
          { show: false }
        ]
      });
      
      printTimerRef.current = setTimeout(() => {
         // 获取图表截图
    const img = window.echartsInstance.getDataURL()
    
    const html = `
   <!DOCTYPE html>
   <html>
   <head>
     <title>打印 - ${chartData[0].name}</title>
     <style>
       body {
         font-family: Arial, sans-serif;
         margin: 20px;
       }
       h1 {
         font-size: 18px;
         text-align: center;
         margin-bottom: 20px;
       }
       table {
         width: 100%;
         border-collapse: collapse;
         margin-bottom: 20px;
       }
       th, td {
         border: 1px solid #ddd;
         padding: 8px;
         text-align: center;
       }
       th {
         background-color: #f2f2f2;
       }
       .chart-container {
         width: 100%;
         height: 300px;
         border: 1px solid #ddd;
         padding: 10px;
         box-sizing: border-box;
         margin-bottom: 20px;
       }
       #chart-placeholder {
         width: 100%;
         height: 300px;
         border: 1px solid #ddd;
         display: flex;
         align-items: center;
         justify-content: center;
         margin-bottom: 20px;
       }
       @media print {
         @page {
           size: auto;
           margin: 10mm;
         }
       }
     </style>
   </head>
   <body>
     <h1>${chartData[0].name}</h1>
     <div id="chart-placeholder"> <img src="${img}" style="width:102%;margin-left:2%"></div>
     ${generateFeature1TableContent()}
     ${generateFeature2TableContent()}
   </body>
   </html>
 `
    // 打印视图
    window.api.printView(html).then(() => {
      // 打印完成后恢复工具箱和缩放控件
      if (window.echartsInstance) {
        window.echartsInstance.setOption({
          grid: {
            left: '10%',
            right: '10%',
            top: '15%',
            bottom: '15%',
            containLabel: true
          },
          toolbox: {
            show: true
          },
          dataZoom: [
            { show: true },
            { show: true },
            { show: true },
            { show: true }
          ]
        });
      }
      setIsPrinting(false)
      printTimerRef.current = null
    }).catch((error) => {
      console.error('打印失败:', error)
      setIsPrinting(false)
      printTimerRef.current = null
    })
      }, 500)
    }
  }, [chartData])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (printTimerRef.current) {
        clearTimeout(printTimerRef.current)
      }
    }
  }, [])

  // 生成功能一表格内容
  const generateFeature1TableContent = (): string => {
    // 如果没有数据，返回空字符串
    if (feature1Data.length === 0) return ''

    // 表格头部
    let tableContent = `
      <h2>功能一数据</h2>
      <table>
        <thead>
          <tr>
          
    `

    // 根据是否有选中的行决定表头内容
    const hasSelectedRows = feature1Data.some((row) => row.selected)

    if (hasSelectedRows) {
      // 如果有选中的行，显示最大值、最小值、平均值
      tableContent += `
            <th>波长起点</th>
            <th>波长终点</th>
            <th>最大值</th>
            <th>最小值</th>
            <th>平均值</th>
          </tr>
        </thead>
        <tbody>
      `

      // 添加选中行的数据
      feature1Data.forEach((row) => {
        if (row.selected) {
          tableContent += `
            <tr>
            
              <td>${row.startWavelength  || 'NA'}</td>
              <td>${row.endWavelength || 'NA'}</td>
              <td>${row.maxValue || 'NA'}</td>
              <td>${row.minValue || 'NA'}</td>
              <td>${row.avgValue || 'NA'}</td>
            </tr>
          `
        }
      })
    } else {
      // 如果没有选中的行，显示波长起点和终点
      tableContent += `
            <th>波长起点</th>
            <th>波长终点</th>
          </tr>
        </thead>
        <tbody>
      `

      // 添加所有行的数据
      feature1Data.forEach((row) => {
        tableContent += `
          <tr>
            <td>${row.id}</td>
            <td>${row.startWavelength || 'NA'}</td>
            <td>${row.endWavelength || 'NA'}</td>
          </tr>
        `
      })
    }

    // 表格结束
    tableContent += `
        </tbody>
      </table>
    `

    return tableContent
  }

  // 生成功能二表格内容
  const generateFeature2TableContent = (): string => {
    // 如果没有数据，返回空字符串
    if (feature2Data.length === 0) return ''

    // 表格头部
    let tableContent = `
      <h2>功能二数据</h2>
      <table>
        <thead>
          <tr>
            
    `

    // 根据是否有选中的行决定表头内容
    const hasSelectedRows = feature2Data.some((row) => row.selected)

    if (hasSelectedRows) {
      // 如果有选中的行，显示中心波长、半高宽等数据
      tableContent += `
            <th>波长起点</th>
            <th>波长终点</th>
            <th>中心波长</th>
            <th>半高宽</th>
            <th>最大透过率Tmax</th>
            <th>50%Tmax波长1</th>
            <th>50%Tmax波长2</th>
            <th>20%Tmax波长1</th>
            <th>20%Tmax波长2</th>
            <th>10%Tmax波长1</th>
            <th>10%Tmax波长2</th>
          </tr>
        </thead>
        <tbody>
      `

      // 添加选中行的数据
      feature2Data.forEach((row) => {
        if (row.selected) {
          tableContent += `
            <tr>
             
              <td>${row.startWavelength  || 'NA'}</td>
              <td>${row.endWavelength || 'NA'}</td>
              <td>${row.centerWavelength || 'NA'}</td>
              <td>${row.halfWidth || 'NA'}</td>
              <td>${row.maxTransmittance || 'NA'}</td>
              <td>${row.wavelength1 || 'NA'}</td>
              <td>${row.wavelength2 || 'NA'}</td>
              <td>${row.wavelength20_1 || 'NA'}</td>
              <td>${row.wavelength20_2 || 'NA'}</td>
              <td>${row.wavelength10_1 || 'NA'}</td>
              <td>${row.wavelength10_2 || 'NA'}</td>
            </tr>
          `
        }
      })
    } else {
      // 如果没有选中的行，显示波长起点和终点
      tableContent += `
            <th>波长起点</th>
            <th>波长终点</th>
          </tr>
        </thead>
        <tbody>
      `

      // 添加所有行的数据
      feature2Data.forEach((row) => {
        tableContent += `
          <tr>
            <td>${row.id}</td>
            <td>${row.startWavelength || 'NA'}</td>
            <td>${row.endWavelength || 'NA'}</td>
          </tr>
        `
      })
    }

    // 表格结束
    tableContent += `
        </tbody>
      </table>
    `

    return tableContent
  }

  return (
    <div className="border-l border-border p-4 bg-card/50 flex flex-col gap-4 flex-1 "
    style={{ height:'calc(100vh - 67px)' }}
    >
      <ControlPanel />
      <Button size="sm" onClick={handlePrint} disabled={isPrinting}>
        <Printer className="h-4 w-4 mr-1" />
        {isPrinting ? '打印中...' : '打印'}
      </Button>
    </div>
  )
}

export default SidePanel
