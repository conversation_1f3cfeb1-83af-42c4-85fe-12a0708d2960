import { JSX, useState } from 'react'
import { ActivationDialog } from './ActivationDialog'
import { useActivation } from '../../lib/ActivationContext'
import { ThemeToggle } from '../ThemeToggle'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs'
import ActivationManager from './ActivationManager'
import { ShieldCheck, ArrowLeft } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export function ActivationPage(): JSX.Element {
  const [showActivationDialog, setShowActivationDialog] = useState(false)
  const [activeTab, setActiveTab] = useState<'client' | 'admin'>('client')
  const { isActivated } = useActivation()
  const navigate = useNavigate()
  const isAdmin = import.meta.env.VITE_ISADMIN === '1'

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-[600px] space-y-4">
        {isAdmin && (
          <div className="flex justify-between items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="flex items-center gap-1"
            >
              <ArrowLeft className="h-4 w-4" />
              返回主页
            </Button>
            <ThemeToggle />
          </div>
        )}

        {isAdmin ? (
          <ActivationManager />
        ) : (
          <ActivationDialog onClose={() => setShowActivationDialog(false)} />
        )}
      </div>
    </div>
  )
}
