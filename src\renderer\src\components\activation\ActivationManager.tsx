import { JSX, useState } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { generateActivationCode } from '../../lib/activationUtils'
import { Loader2, Key, Copy, Check, RefreshCw } from 'lucide-react'

export default function ActivationManager(): JSX.Element {
  const [machineId, setMachineId] = useState('')
  const [activationCode, setActivationCode] = useState('')
  const [expiryDays, setExpiryDays] = useState<number | ''>('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleGenerateCode = () => {
    setError(null)

    if (!machineId.trim()) {
      setError('请输入机器码')
      return
    }

    setIsGenerating(true)

    try {
      // 生成激活码
      const days = typeof expiryDays === 'number' ? expiryDays : undefined
      const code = generateActivationCode(machineId, days)
      setActivationCode(code)
    } catch (err) {
      setError('生成激活码失败')
      console.error('生成激活码失败:', err)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopyCode = () => {
    if (!activationCode) return

    navigator.clipboard
      .writeText(activationCode)
      .then(() => {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      })
      .catch((err) => {
        console.error('复制失败:', err)
        setError('复制到剪贴板失败')
      })
  }

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-md">
          <Key className="h-5 w-5 text-primary" />
          激活码生成器
        </CardTitle>
        <CardDescription>为特定机器生成软件激活码</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">机器码</label>
            <Input
              value={machineId}
              onChange={(e) => setMachineId(e.target.value)}
              placeholder="输入客户的机器码"
              className="font-mono"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">有效期（天）</label>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={expiryDays}
                onChange={(e) => {
                  const value = e.target.value
                  setExpiryDays(value === '' ? '' : parseInt(value, 10))
                }}
                placeholder="留空表示永久有效"
                min="1"
                className="w-full"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => setExpiryDays('')}
                className="whitespace-nowrap"
              >
                永久
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              {expiryDays ? `激活码将在 ${expiryDays} 天后过期` : '激活码永久有效'}
            </p>
          </div>

          {activationCode && (
            <div className="space-y-2 pt-2">
              <label className="text-sm font-medium">生成的激活码</label>
              <div className="flex">
                <Input value={activationCode} readOnly className="font-mono text-xs bg-muted" />
                <Button variant="outline" size="sm" className="ml-2" onClick={handleCopyCode}>
                  {copied ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )}

          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-2 rounded-md">{error}</div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => {
            setMachineId('')
            setActivationCode('')
            setExpiryDays('')
            setError(null)
          }}
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          重置
        </Button>
        <Button onClick={handleGenerateCode} disabled={isGenerating || !machineId.trim()}>
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              生成中...
            </>
          ) : (
            <>生成激活码</>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
