import { ElectronAPI } from '@electron-toolkit/preload'

// 激活API接口定义
interface ActivationAPI {
  getMachineId: () => Promise<string>
  updateActivationStatus: (status: {
    isLoading: boolean
    isActivated: boolean
    activationType: 'trial' | 'permanent' | null
    remainingDays: number | null
  }) => void
}

// 文件操作API接口定义
interface FileOperationsAPI {
  openFileDialog: (options: any) => Promise<{ canceled: boolean; filePaths?: string[] }>
  readFile: (filePath: string) => Promise<Buffer>
  onMenuEvent: (callback: (event: string, data: any) => void) => void
}

// 应用API接口定义
interface AppAPI {
  printView(html: string): unknown
  activation: ActivationAPI
  fileOperations: FileOperationsAPI
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: AppAPI
  }
}
