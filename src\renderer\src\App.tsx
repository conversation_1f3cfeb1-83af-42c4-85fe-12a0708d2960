import React from 'react'
import { ActivationCheck } from './components/activation/ActivationCheck'
import Header from './components/Header'
import ChartSection from './components/ChartSection'
import DataSection from './components/DataSection'
import SidePanel from './components/SidePanel'
import { ChartProvider } from './contexts/ChartContext'
import FilePath from './components/FilePath'

// 错误边界类组件
class AppErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('应用程序错误:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center h-screen bg-background">
          <div className="text-center p-8">
            <h1 className="text-2xl font-bold mb-4">应用程序遇到错误</h1>
            <p className="text-muted-foreground mb-4">请尝试重新启动应用程序</p>
            <button 
              onClick={() => this.setState({ hasError: false })}
              className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              重试
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// 主应用组件
function App(): React.JSX.Element {
  return (
    <AppErrorBoundary>
      <ActivationCheck>
        <ChartProvider>
          <div className="flex flex-col h-screen overflow-hidden bg-background">
            <Header />
            <div className="grid grid-cols-5 flex-1">
              <div className="flex flex-col flex-1 p-4 space-y-4 col-span-4">
                <FilePath />
                <ChartSection />
                <DataSection />
              </div>
              <SidePanel />
            </div>
          </div>
        </ChartProvider>
      </ActivationCheck>
    </AppErrorBoundary>
  )
}

export default App
