import React, { createContext, useContext, useReducer, ReactNode } from 'react'
import { chartReducer, initialChartState, ChartState, ChartAction } from '../reducers/chartReducer'

// 创建 Context
type ChartContextType = {
  state: ChartState
  dispatch: React.Dispatch<ChartAction>
}

const ChartContext = createContext<ChartContextType | undefined>(undefined)

// Context Provider 组件
interface ChartProviderProps {
  children: ReactNode
}

export const ChartProvider: React.FC<ChartProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(chartReducer, initialChartState)

  return <ChartContext.Provider value={{ state, dispatch }}>{children}</ChartContext.Provider>
}

// 自定义 Hook 用于访问 Context
export const useChart = (): ChartContextType => {
  const context = useContext(ChartContext)
  if (context === undefined) {
    throw new Error('useChart must be used within a ChartProvider')
  }
  return context
}
