import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Calculate value based on data mode
export function calculateValueForMode(
  value: number,
  dataMode: 'T' | 'R' | 'Abs',
  isFromAbs = false
): number {
  if (dataMode === 'Abs') {
    return isFromAbs ? value : -Math.log10(value / 100)
  } else {
    return isFromAbs ? 100 * Math.pow(10, -value) : value
  }
}

// Linear interpolation between two points
export function linearInterpolate(
  x: number,
  x1: number,
  y1: number,
  x2: number,
  y2: number
): number {
  if (x2 === x1) return y1
  return y1 + ((y2 - y1) * (x - x1)) / (x2 - x1)
}

// Format a number to 4 decimal places
export function formatValue(value: number | null): string {
  if (value === null) return 'N/A'
  return value.toFixed(4)
}
