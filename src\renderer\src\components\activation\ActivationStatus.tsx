import React from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { useActivation } from '../../lib/ActivationContext'
import { ShieldCheck, AlertCircle, Clock, X, ArrowLeft, Zap, Crown } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { Progress } from '../ui/progress'
import dayjs from 'dayjs'
interface ActivationStatusProps {
  onActivate?: () => void
  onClose?: () => void
}

export function ActivationStatus({
  onActivate,
  onClose
}: ActivationStatusProps): React.JSX.Element {
  const { isActivated, activationType, expiryDate, isLoading } = useActivation()
  const navigate = useNavigate()

  const getRemainingDays = (): number | null => {
    if (!expiryDate) return null
    const now = new Date()
    const diffTime = Math.max(0, expiryDate.getTime() - now.getTime())

    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  const remainingDays = getRemainingDays()

  // 获取总试用天数
  const getTotalTrialDays = () => {
    if (!expiryDate || activationType !== 'trial') return 0
    return Math.ceil(dayjs(expiryDate).diff(dayjs(), 'day'))
  }

  const totalTrialDays = getTotalTrialDays()

  // 计算试用进度（已用天数百分比）
  const trialProgress =
    expiryDate && activationType === 'trial' && remainingDays !== null
      ? ((totalTrialDays - remainingDays) / totalTrialDays) * 100
      : 0

  const handleActivate = (): void => {
    navigate('/activation')
  }

  // 获取状态颜色
  const getStatusColor = () => {
    if (isLoading) return 'text-gray-500'
    if (isActivated) {
      return activationType === 'permanent' ? 'text-emerald-600' : 'text-blue-600'
    }
    return 'text-amber-600'
  }

  return (
    <div className="fixed inset-0 bg-gray-100 bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <Card className="shadow-xl w-full max-w-md border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
        <CardHeader className="pb-3 pt-5 px-5 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/')}
                className="rounded-full"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <span>激活状态 </span>
              </CardTitle>
            </div>
            {onClose && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full text-muted-foreground"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="px-6 py-6">
          {isLoading ? (
            <div className="flex flex-col items-center py-8 space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              <p className="text-lg text-muted-foreground">正在验证许可证...</p>
            </div>
          ) : isActivated ? (
            <div className="space-y-6">
              <div className={`flex flex-col items-center ${getStatusColor()}`}>
                <div className="relative">
                  <ShieldCheck className="h-16 w-16 mb-3" />
                  {activationType === 'permanent' && (
                    <Crown className="h-6 w-6 absolute -top-1 -right-1 text-yellow-500 fill-yellow-400" />
                  )}
                </div>
                <h3 className="text-2xl font-bold mb-1">
                  {activationType === 'permanent' ? '已激活永久版' : '试用版已激活'}
                </h3>
                <p className="text-muted-foreground">
                  {activationType === 'permanent' ? '感谢您使用专业版' : '解锁全部高级功能'}
                </p>
              </div>

              {activationType === 'trial' && remainingDays !== null && (
                <div className="space-y-4 mt-4">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">
                      失效时间 {dayjs(expiryDate).format('YYYY-MM-DD')}
                    </span>
                    <span className="font-medium">
                      剩余 {remainingDays} 天 
                    </span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6 text-center">
              <div className="flex flex-col items-center text-amber-600">
                <AlertCircle className="h-16 w-16 mb-3" />
                <h3 className="text-2xl font-bold mb-1">未激活</h3>
                <p className="text-muted-foreground max-w-md">您需要激活软件才能使用全部功能</p>
              </div>

              <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg border border-amber-100 dark:border-amber-800/50">
                <ul className="space-y-2 text-sm text-left text-amber-700 dark:text-amber-300">
                  <li className="flex items-start gap-2">
                    <Zap className="h-4 w-4 mt-0.5 text-amber-500 flex-shrink-0" />
                    <span>无法使用高级功能</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Zap className="h-4 w-4 mt-0.5 text-amber-500 flex-shrink-0" />
                    <span>部分操作受到限制</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Zap className="h-4 w-4 mt-0.5 text-amber-500 flex-shrink-0" />
                    <span>无法保存高级设置</span>
                  </li>
                </ul>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex flex-col gap-3 px-6 pb-6">
          {!isLoading && !isActivated && (
            <Button
              size="lg"
              className="w-full py-6 text-base font-bold bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 shadow-md"
              onClick={handleActivate}
            >
              立即激活
            </Button>
          )}

          {isActivated && activationType === 'trial' && (
            <div className="flex gap-3 w-full">
              <Button
                variant="outline"
                className="flex-1 border-blue-500 text-blue-600 hover:bg-blue-50"
                onClick={() => navigate('/')}
              >
                关闭
              </Button>
              <Button className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-700 shadow-md">
                联系管理员升级永久版
              </Button>
            </div>
          )}

          {isActivated && activationType === 'permanent' && onClose && (
            <Button variant="secondary" className="w-full" onClick={onClose}>
              完成
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
