import { v4 as uuidv4 } from 'uuid'

// 机器码生成函数
export async function getMachineId(): Promise<string> {
  try {
    // 通过预加载脚本中定义的API获取机器码
    const machineId = await window.api.activation.getMachineId()
    return machineId || generateFallbackMachineId()
  } catch (error) {
    console.error('获取机器码失败:', error)
    return generateFallbackMachineId()
  }
}

// 生成备用机器码（仅用于演示）
function generateFallbackMachineId(): string {
  // 在实际应用中，应该使用更可靠的方法获取机器唯一标识
  // 这里仅用于演示
  const storedId = localStorage.getItem('machine_id')
  if (storedId) return storedId

  const newId = uuidv4()
  localStorage.setItem('machine_id', newId)
  return newId
}

// 生成激活码
export function generateActivationCode(machineId: string, expiryDays?: number): string {
  // 实际应用中应使用更复杂的算法和加密方式
  // 这里使用简化的方式，仅用于演示
  const activationTimestamp = Date.now()

  // 创建包含机器码和激活信息的数据
  const data = {
    machineId,
    activationTimestamp,
    trialDays: expiryDays || 0,
    type: expiryDays ? 'trial' : 'permanent'
  }

  // 在实际应用中，这里应该使用加密算法
  // 这里简单地将数据转为JSON并进行Base64编码
  const jsonData = JSON.stringify(data)
  // 使用浏览器内置的 btoa 函数进行 Base64 编码，替代 Node.js 的 Buffer
  const encodedData = btoa(jsonData)

  // 添加简单的校验码（实际应用中应使用更安全的方法）
  const checksum = calculateChecksum(encodedData)

  return `${encodedData}.${checksum}`
}

// 验证激活码
export function validateActivationCode(
  code: string,
  machineId: string
): {
  valid: boolean
  expired?: boolean
  type?: 'trial' | 'permanent'
  expiryDate?: Date
} {
  try {
    // 分离数据和校验码
    const [encodedData, checksum] = code.split('.')

    // 验证校验码
    if (calculateChecksum(encodedData) !== checksum) {
      return { valid: false }
    }

    // 解码数据，使用浏览器内置的 atob 函数，替代 Node.js 的 Buffer
    const jsonData = atob(encodedData)
    const data = JSON.parse(jsonData)

    // 验证机器码
    if (data.machineId !== machineId) {
      return { valid: false }
    }
  
    // 检查是否过期（仅对试用版）
    if (data.type === 'trial') {
      const result = checkTrialExpiry(data.activationTimestamp, data.trialDays)
      if (result.expired) {
        return {
          valid: false,
          expired: true,
          type: 'trial',
          expiryDate: result.expiryDate
        }
      }
    }

    return {
      valid: true,
      type: data.type,
      expiryDate: data.type === 'trial' ? new Date(data.activationTimestamp + data.trialDays * 24 * 60 * 60 * 1000) : undefined
    }
  } catch (error) {
    console.error('验证激活码失败:', error)
    return { valid: false }
  }
}

// 计算简单的校验码（实际应用中应使用更安全的方法）
function calculateChecksum(data: string): string {
  let sum = 0
  for (let i = 0; i < data.length; i++) {
    sum += data.charCodeAt(i)
  }
  return sum.toString(16)
}

// 防篡改的试用期检查函数
function checkTrialExpiry(activationTimestamp: number, trialDays: number): {
  expired: boolean
  expiryDate: Date
} {
  const expiryDate = new Date(activationTimestamp + trialDays * 24 * 60 * 60 * 1000)
  const currentTime = Date.now()
  
  // 获取上次检查的时间记录
  const lastCheckKey = 'last_activation_check'
  const usageDaysKey = 'activation_usage_days'
  const lastCheck = localStorage.getItem(lastCheckKey)
  const usageDays = parseFloat(localStorage.getItem(usageDaysKey) || '0')
  
  if (lastCheck) {
    const lastCheckTime = parseInt(lastCheck)
    const timeDiff = currentTime - lastCheckTime
    
    // 如果系统时间被调回过去（时间差为负数），则认为可能存在篡改
    if (timeDiff < 0) {
      console.warn('检测到系统时间可能被篡改')
      // 不更新使用天数，直接基于已累计的使用天数判断
    } else {
      // 正常情况下，累加使用天数
      const additionalDays = timeDiff / (24 * 60 * 60 * 1000)
      const newUsageDays = usageDays + additionalDays
      localStorage.setItem(usageDaysKey, newUsageDays.toString())
    }
  } else {
    // 首次检查，初始化使用天数
    localStorage.setItem(usageDaysKey, '0')
  }
  
  // 更新最后检查时间
  localStorage.setItem(lastCheckKey, currentTime.toString())
  
  // 获取最新的使用天数
  const finalUsageDays = parseFloat(localStorage.getItem(usageDaysKey) || '0')
  
  return {
    expired: finalUsageDays >= trialDays,
    expiryDate
  }
}

// 保存激活信息
export function saveActivationInfo(code: string, machineId: string): void {
  localStorage.setItem('activation_code', code)
  localStorage.setItem('machine_id', machineId)
}

// 获取保存的激活信息
export function getSavedActivationInfo(): { code: string | null; machineId: string | null } {
  return {
    code: localStorage.getItem('activation_code'),
    machineId: localStorage.getItem('machine_id')
  }
}

// 检查软件是否已激活
export async function checkActivation(): Promise<{
  activated: boolean
  type?: 'trial' | 'permanent'
  expiryDate?: Date
}> {
  const { code, machineId } = getSavedActivationInfo()

  if (!code || !machineId) {
    return { activated: false }
  }

  const currentMachineId = await getMachineId()

  // 验证机器码是否匹配当前机器
  if (machineId !== currentMachineId) {
    return { activated: false }
  }

  const result = validateActivationCode(code, machineId)

  return {
    activated: result.valid,
    type: result.type,
    expiryDate: result.expiryDate
  }
}
