import { ipcMain } from 'electron'
import pkg from 'node-machine-id'
const { machineIdSync } = pkg
import * as os from 'os'
import * as crypto from 'crypto'
import {startPrint} from 'electron-print-preview'
// 初始化激活相关的IPC处理程序
export function setupActivationHandlers() {
  // 获取机器码
  ipcMain.handle('get-machine-id', async () => {
    try {
      // 尝试使用node-machine-id获取机器ID
      return getMachineId()
    } catch (error) {
      console.error('获取机器ID失败:', error)
      // 使用备用方法
      return getFallbackMachineId()
    }
  })
  ipcMain.handle('print-view', (_,html) => {
   
    startPrint({htmlString: html})
  })
}

// 获取机器ID的主要方法
function getMachineId(): string {
  try {
    // 使用node-machine-id库获取机器ID
    return machineIdSync(true)
  } catch (error) {
    console.error('使用machineIdSync获取机器ID失败:', error)
    throw error
  }
}

// 备用方法：基于系统信息生成机器ID
function getFallbackMachineId(): string {
  try {
    // 收集系统信息
    const cpus = os.cpus()
    const networkInterfaces = os.networkInterfaces()
    const hostname = os.hostname()
    const platform = os.platform()
    const arch = os.arch()
    const totalMemory = os.totalmem()

    // 提取MAC地址（如果可用）
    const macAddresses: string[] = []
    Object.values(networkInterfaces).forEach((interfaces) => {
      if (!interfaces) return
      interfaces.forEach((iface) => {
        if (!iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac)
        }
      })
    })

    // 创建一个包含所有信息的字符串
    const systemInfo = JSON.stringify({
      cpu: cpus.length > 0 ? cpus[0].model : '',
      cpuCount: cpus.length,
      hostname,
      platform,
      arch,
      totalMemory,
      macAddresses
    })

    // 使用SHA-256哈希生成唯一ID
    const hash = crypto.createHash('sha256')
    hash.update(systemInfo)
    return hash.digest('hex')
  } catch (error) {
    console.error('生成备用机器ID失败:', error)
    // 最后的备用方案：生成一个随机ID
    return crypto.randomBytes(32).toString('hex')
  }
}
