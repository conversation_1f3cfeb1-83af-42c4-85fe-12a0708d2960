import * as React from 'react'
import { cn } from '../../lib/utils' // 假设你有工具类函数

interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number // 0-100
  max?: number
  indicatorColor?: string
  trackColor?: string
  height?: number
  showLabel?: boolean
  labelPosition?: 'inside' | 'outside'
  animated?: boolean
}

const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  (
    {
      className,
      value = 0,
      max = 100,
      indicatorColor = 'bg-primary',
      trackColor = 'bg-muted',
      height = 8,
      showLabel = false,
      labelPosition = 'inside',
      animated = true,
      ...props
    },
    ref
  ) => {
    const percentage = Math.min(100, Math.max(0, (value / max) * 100))
    const progressRef = React.useRef<HTMLDivElement>(null)

    // 动画效果
    React.useEffect(() => {
      if (!animated || !progressRef.current) return

      progressRef.current.style.width = '0%'
      const timeout = setTimeout(() => {
        if (progressRef.current) {
          progressRef.current.style.width = `${percentage}%`
        }
      }, 50)

      return () => clearTimeout(timeout)
    }, [percentage, animated])

    return (
      <div className="flex flex-col w-full gap-2">
        <div
          ref={ref}
          className={cn('relative w-full overflow-hidden rounded-full', trackColor, className)}
          style={{ height: `${height}px` }}
          {...props}
        >
          <div
            ref={progressRef}
            className={cn(
              'h-full rounded-full transition-all duration-500 ease-out',
              indicatorColor,
              animated ? 'transition-all duration-500 ease-out' : ''
            )}
            style={{
              width: animated ? undefined : `${percentage}%`
            }}
          >
            {showLabel && labelPosition === 'inside' && (
              <span
                className={cn(
                  'absolute right-2 top-1/2 -translate-y-1/2 text-xs',
                  percentage > 50 ? 'text-white' : 'text-foreground'
                )}
              >
                {Math.round(percentage)}%
              </span>
            )}
          </div>
        </div>

        {showLabel && labelPosition === 'outside' && (
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round(percentage)}%</span>
          </div>
        )}
      </div>
    )
  }
)

Progress.displayName = 'Progress'

export { Progress }
