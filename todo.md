Okay, let's break down your software development requirements into a structured document.

## 软件需求规格说明书 (SRS) - 光谱分析软件

**版本:** 1.0
**日期:** 2023年10月27日

---

### 1. 引言

#### 1.1 目的

本文档旨在详细描述光谱分析软件的功能需求、非功能需求、界面需求及数据处理逻辑。该软件用于显示、分析和报告光谱数据，主要涉及透过率(T%)、反射率(R%)和吸光度(Abs)与波长之间的关系。

#### 1.2 范围

本软件将支持：

- 光谱数据（波长 vs T%/R%/Abs）的加载与显示。
- T%、R%、Abs三种模式之间的转换与计算。
- 图谱显示的高度自定义化（谱线选择、颜色、坐标轴、网格等）。
- 图谱上特定数据点的标注与交互。
- 指定波长范围内的统计值计算（最大/最小/平均值）。
- 指定波长范围内的峰值参数分析（峰值、半峰宽相关参数）。
- 生成包含图谱和分析数据的A4报告。

#### 1.3 定义、首字母缩写词和缩略语

- **T%**: 透过率百分比
- **R%**: 反射率百分比
- **Abs**: 吸光度
- **WL**: Wavelength (波长)
- **UI**: User Interface (用户界面)
- **N/A**: Not Applicable / Not Available (不适用/无数据)
- **FWHM**: Full Width at Half Maximum (半高全宽)

#### 1.4 参考资料

- 用户提供的需求描述文档。
- 用户提供的参考图片。

#### 1.5 文档概述

本文档后续章节将详细描述软件的整体功能、具体功能需求、界面指南、数据处理规则以及报告格式。

---

### 2. 总体描述

#### 2.1 产品视图

本软件是一个独立的光谱数据显示和分析工具，旨在为用户提供便捷的光谱数据可视化、转换、分析和报告功能。

#### 2.2 产品功能 (摘要)

1.  **数据模式转换**: 在T%, R%, Abs之间切换显示模式并进行相应数据计算。
2.  **图谱显示与定制**: 显示光谱曲线，并允许用户自定义谱线、坐标轴、字体、网格、图谱大小和样品名称显示。
3.  **数据点标注**: 允许用户在图谱上点击标注特定点，并显示其坐标，标签可移动和删除。
4.  **范围统计分析**: 计算指定波长范围内的最大值、最小值和平均值。
5.  **峰值参数分析**: 在指定波长范围内，分析峰的最大透过率、中心波长、50%透过率对应波长等。
6.  **报告生成**: 生成包含图谱和分析结果的A4报告。

#### 2.3 用户特征

用户为熟悉光谱数据分析的科研人员或技术人员，对光谱图的基本概念（波长、透过率、反射率、吸光度、峰值等）有一定了解。

#### 2.4 约束

- 输入数据格式（未指定，假设为常见的文本格式如CSV, TXT，包含波长和对应的Y值）。
- 软件运行环境（未指定，假设为Windows桌面应用）。

#### 2.5 假设与依赖

- 光谱数据点是按波长顺序排列的。
- 用户输入的波长范围是有效的。

---

### 3. 具体需求

#### 3.1 数据格式与精度

- **横坐标 (波长)**: 通常取整数或小数点后1位。
- **纵坐标 (T%/R%/Abs)**: 通常取整数或小数点后1位。
- **数据显示精度**: 所有在UI中显示（如标注、表格）的数值，应显示小数点后4位。

#### 3.2 功能需求

##### 3.2.1 数据模式转换 (FR-1)

系统应在界面顶部第一排提供T, R, Abs转换按钮。

- **FR-1.1 T% 到 R%**:
  - **触发**: 用户点击 "R%" 按钮，当前模式为 T%。
  - **动作**: 纵坐标轴标签从 "T%" 变为 "R%"。光谱数据本身不发生变化。
- **FR-1.2 R% 到 T%**:
  - **触发**: 用户点击 "T%" 按钮，当前模式为 R%。
  - **动作**: 纵坐标轴标签从 "R%" 变为 "T%"。光谱数据本身不发生变化。
- **FR-1.3 T% 或 R% 到 Abs**:
  - **触发**: 用户点击 "Abs" 按钮，当前模式为 T% 或 R%。
  - **动作**:
    1.  纵坐标轴标签变为 "Abs"。
    2.  光谱数据根据公式 `Abs = -logY` 进行转换，其中 Y 为原始的 T% 或 R% 值。
    3.  示例：若原始 T 值为 10 (代表10%)，转换后 Abs 值为 1.0000。
- **FR-1.4 Abs 到 T% 或 R%**:
  - **触发**: 用户点击 "T%" 或 "R%" 按钮，当前模式为 Abs。
  - **动作**:
    1.  纵坐标轴标签根据点击的按钮变为 "T%" 或 "R%"。
    2.  光谱数据根据公式 `Y = (10^(-Abs)) * 100` 进行转换，其中 Abs 为原始吸光度值。
    3.  示例：若原始 Abs 值为 2.0000，转换后 T% 或 R% 值为0.01 (即1%)。
    

##### 3.2.2 图谱显示与定制 (FR-2)

系统应提供与现有（或通用）光谱软件类似的图谱定制功能。

- **FR-2.1 谱线选择**: 用户可以选择显示或隐藏加载的多条谱线中的任意一条。
- **FR-2.2 谱线命名**: 用户可以修改已加载谱线的名称。
- **FR-2.3 坐标轴范围**: 用户可以手动设置横坐标（波长）和纵坐标（T%/R%/Abs）的显示范围（最小值和最大值）。
- **FR-2.4 字体大小**: 用户可以设置图谱中各项文本元素（如轴标签、标题、图例）的字体大小。
- **FR-2.5 网格线**:
  - 用户可以控制主网格线的显示或隐藏。
  - 用户可以控制次网格线（网格线密度）的显示或隐藏/调整密度。
- **FR-2.6 图谱缩放**: 用户可以通过拖动图谱边框或特定控件来调整图谱在界面中的显示大小。
- **FR-2.7 样品名称显示**: 用户可以将样品名称（或谱线名称）显示在图谱内部的左上角或右上角。

##### 3.2.3 数据点标注 (FR-3)

系统应允许用户在图谱上标注特定点。

- **FR-3.1 标注触发**: 用户通过鼠标点击图谱上的曲线某一点。
- **FR-3.2 标注显示**:
  - 在被点击点处显示一个可视化标记（如圆点、星号，或两者结合）。标记颜色应可区分。
  - 在标记附近显示一个标签，内容为该点的横坐标（波长）和纵坐标（当前模式下的T%/R%/Abs值），数值显示4位小数。
  - 可选：可以有一条指示线从标签指向标记点。
- **FR-3.3 标签交互**:
  - 用户可以通过鼠标拖动来改变标签的位置，以避免遮挡。
  - 用户可以通过鼠标选择并删除已添加的标注（标记和标签）。

##### 3.2.4 范围统计分析 (FR-4)

系统应提供一个对话框（或界面区域），包含至少5行输入/显示空间，用于波长范围内的统计计算。

- **FR-4.1 输入**:
  - 每行允许用户输入“波长起点”和“波长终点”。
- **FR-4.2 自动计算与显示**:
  - 对于用户在每一行输入的波长范围，软件自动计算该范围内（针对当前活动或选定的谱线）的：
    - 纵坐标最大值
    - 纵坐标最小值
    - 纵坐标平均值（范围内所有数据点Y值的算术平均）
  - 这些计算结果（4位小数）显示在对应的行和列中。
- **FR-4.3 报告勾选**:
  - 每种计算值（最大值、最小值、平均值）前应有勾选框。
  - 默认所有勾选框均为选中状态。
  - 只有勾选了的统计值才会包含在最终的报告中。
- **FR-4.4 UI布局参考**:
  | 波长起点 | 波长终点 | 勾选 | 最大值 | 勾选 | 最小值 | 勾选 | 平均值 |
  | -------- | -------- | ---- | ------ | ---- | ------ | ---- | ------ |
  | (输入) | (输入) | [√] | (计算值) | [√] | (计算值) | [√] | (计算值) |
  | ... | ... | ... | ... | ... | ... | ... | ... | (共5行)

##### 3.2.5 峰值参数分析 (FR-5)

系统应提供一个对话框（或界面区域），包含至少5行输入/显示空间，用于峰值参数的分析。

- **FR-5.1 输入**:
  - 每行允许用户输入“波长起点”和“波长终点”来定义一个分析区域。
  - **FR-5.1.1 鼠标拖动输入 (可选增强)**: 如果可能，允许用户在图谱上通过鼠标拖动选择一个波长范围，该范围的起点和终点自动填充到输入框中。
- **FR-5.2 自动计算与显示 (针对T%模式下的峰，或当前模式下的极值)**:
  对于用户在每一行输入的波长范围（针对当前活动或选定的谱线）：
  1.  **中心波长 (输入范围)**: `(波长起点 + 波长终点) / 2` nm。
  2.  **范围宽度 (输入范围)**: `波长终点 - 波长起点` nm。 (用户表格中的“半高宽”定义为此)
  3.  **最大透过率 (Tmax)**: 在此输入波长范围内找到的实际最大透过率值 (或当前Y轴模式下的峰值)。同时记录此 Tmax 对应的**波长**。
  4.  **50% Tmax 波长1**: 在 Tmax 波长的左侧（较小波长），找到透过率等于 `0.5 * Tmax` 的波长。
  5.  **50% Tmax 波长2**: 在 Tmax 波长的右侧（较大波长），找到透过率等于 `0.5 * Tmax` 的波长。
      _(注: 若当前为Abs模式，则应寻找Abs最小值作为峰，或提示用户切换到T%/R%模式进行此分析，或基于Tmax的定义进行内部转换计算。需求明确为“最大透过率Tmax”，故优先基于T%数据。)_
- **FR-5.3 插值**: 在计算 50% Tmax 波长时，如果数据点不精确落在 50% Tmax 上，应使用线性插值进行计算。
- **FR-5.4 N/A显示**: 如果在指定范围内无法有效计算出峰值参数（例如，不是一个峰，或找不到50% Tmax点），则对应参数显示 "N/A"。
- **FR-5.5 谱图标注**:
  - 根据用户勾选，可以将计算出的 Tmax 点、50% Tmax 波长点1、50% Tmax 波长点2 及其对应的Y值标注在图谱上。
  - 标注样式应符合 FR-3 的要求（标记、标签、可移动、可删除）。
- **FR-5.6 报告勾选**:
  - 每个计算参数前应有勾选框，用于选择是否在报告中打印。
- **FR-5.7 UI布局参考**:
  | 勾选 | 波长起点 | 波长终点 | 中心波长 ((大+小)/2 nm) | 范围宽度 ((大-小) nm) | (勾选) 最大透过率Tmax (及其波长) | (勾选) 50%Tmax波长1 (nm) | (勾选) 50%Tmax波长2 (nm) | 备注 |
  | ---- | -------- | -------- | ---------------------- | ------------------- | ------------------------------- | ------------------------- | ------------------------- | ---- |
  | [√] | (输入) | (输入) | (计算值) | (计算值) | (计算值, 计算值) @ WL*peak | (计算值) | (计算值) | (N/A 或空) |
  | ... | ... | ... | ... | ... | ... | ... | ... | ... | (共5行)
  *(注: “半高宽”列根据用户描述是输入范围的宽度，而非实际FWHM。实际FWHM可由 50%Tmax波长2 - 50%Tmax波长1 计算得到，但未在表格中明确列出)\_

##### 3.2.6 报告生成 (FR-6)

系统应能生成包含图谱和分析数据的报告。

- **FR-6.1 纸张大小**: A4。
- **FR-6.2 纸张方向**: 用户可以选择横向 (Landscape) 或纵向 (Portrait)。
- **FR-6.3 报告内容与布局**:
  - **上部分**: 显示当前配置的谱图。谱图应尽可能清晰地占据可用空间。
  - **下部分**: 显示 FR-4 (范围统计分析) 和 FR-5 (峰值参数分析) 中用户勾选要打印的计算结果表格。
- **FR-6.4 空间利用**:
  - 报告内容（图谱和表格）应尽量扩展以占满A4纸张。
  - 如果选择竖版且内容不足以占满页面，则下方留空。

#### 3.3 用户界面 (UI) 需求 (UI-1)

- **UI-1.1 易用性**: 界面应直观易懂，常用功能应易于访问。
- **UI-1.2 一致性**: 界面元素、按钮样式、对话框等应保持一致性。
- **UI-1.3 反馈**: 对于耗时操作，应有进度提示；对于用户操作，应有明确的视觉反馈。

#### 3.4 数据处理需求 (DP-1)

- **DP-1.1 数值精度**: 所有内部计算应保持足够的精度，以确保最终显示的4位小数准确。
- **DP-1.2 插值算法**: 线性插值应用于FR-5.3。

#### 3.5 非功能性需求

##### 3.5.1 性能 (NFR-1)

- **NFR-1.1 响应速度**: 图谱模式转换、参数计算应在几秒内完成，避免用户长时间等待。
- **NFR-1.2 图谱渲染**: 图谱的重绘（如缩放、范围更改）应流畅。

##### 3.5.2 可用性 (NFR-2)

- **NFR-2.1 错误处理**: 对于无效输入或无法进行的计算（如在平坦区域找峰），应给出明确提示（如 "N/A" 或提示信息）。

---

### 4. 附录

(可在此处添加用户提供的参考图片或其他相关材料)

---

这份SRS文档应该能比较清晰地表达您的需求。在实际开发前，建议与开发团队再次确认每个细节，尤其是存在多重解释或需要具体设计决策的地方（如Abs到T%的转换示例、峰值分析中“半高宽”的具体定义等）。
