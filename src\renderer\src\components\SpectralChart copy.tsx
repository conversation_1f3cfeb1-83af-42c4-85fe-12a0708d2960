import React, { useEffect, useRef, useState } from 'react'
import * as d3 from 'd3'
import { SpectralDataPoint, SpectralSeries } from '../lib/spectralUtils'

interface SpectralChartProps {
  mode: 'T' | 'R' | 'Abs'
  data: SpectralSeries[]
}

interface Annotation {
  id: string
  x: number
  y: number
  labelX: number
  labelY: number
  wavelength: number
  value: number
}

const SpectralChart: React.FC<SpectralChartProps> = ({ mode, data }) => {
  const chartRef = useRef<SVGSVGElement>(null)
  const [annotations, setAnnotations] = useState<Annotation[]>([])
  const [draggedAnnotation, setDraggedAnnotation] = useState<string | null>(null)

  // Sample data for demonstration
  const sampleData: SpectralSeries[] = [
    {
      id: '1',
      name: 'Sample 1',
      mode: 'T',
      color: 'steelblue',
      visible: true,
      data: [
        { wavelength: 1, value: 2 },
        { wavelength: 1.5, value: 2.5 },
        { wavelength: 2, value: 3 },
        { wavelength: 3, value: 3.5 },
        { wavelength: 4, value: 4.8 },
        { wavelength: 5, value: 5.2 },
        { wavelength: 6, value: 6.0 }
      ]
    }
  ]

  // Use sample data if no data is provided
  const chartData = data.length > 0 ? data : sampleData

  const handleChartClick = (event: React.MouseEvent<SVGSVGElement>) => {
    if (!chartRef.current) return

    const svg = d3.select(chartRef.current)
    const margin = { top: 20, right: 30, bottom: 40, left: 50 }
    const width = chartRef.current.clientWidth - margin.left - margin.right
    const height = chartRef.current.clientHeight - margin.top - margin.bottom

    // Get mouse position relative to SVG
    const [mouseX, mouseY] = d3.pointer(event)

    // 计算 X 轴和 Y 轴的范围
    let xMin = Infinity
    let xMax = -Infinity
    let yMin = Infinity
    let yMax = -Infinity

    // 遍历所有可见的数据系列，找出 X 和 Y 的最小值和最大值
    chartData.forEach((series) => {
      if (!series.visible || !series.data.length) return

      series.data.forEach((point) => {
        xMin = Math.min(xMin, point.wavelength)
        xMax = Math.max(xMax, point.wavelength)
        yMin = Math.min(yMin, point.value)
        yMax = Math.max(yMax, point.value)
      })
    })

    // 如果没有有效数据，使用默认范围
    if (xMin === Infinity || xMax === -Infinity) {
      xMin = 1
      xMax = 6
    }

    if (yMin === Infinity || yMax === -Infinity) {
      yMin = 2
      yMax = 6
    }

    // 为坐标轴添加一些边距
    const xPadding = (xMax - xMin) * 0.05
    const yPadding = (yMax - yMin) * 0.05

    // Convert mouse position to data coordinates
    const xScale = d3
      .scaleLinear()
      .domain([xMin - xPadding, xMax + xPadding])
      .range([0, width])

    const yScale = d3
      .scaleLinear()
      .domain([yMin - yPadding, yMax + yPadding])
      .range([height, 0])

    const xValue = xScale.invert(mouseX - margin.left)
    const yValue = yScale.invert(mouseY - margin.top)

    // Find the closest data point
    let closestPoint: SpectralDataPoint | null = null
    let minDistance = Infinity
    let seriesIndex = 0

    chartData.forEach((series, idx) => {
      if (!series.visible) return

      series.data.forEach((point) => {
        const dx = xScale(point.wavelength) - (mouseX - margin.left)
        const dy = yScale(point.value) - (mouseY - margin.top)
        const distance = Math.sqrt(dx * dx + dy * dy)

        if (distance < minDistance) {
          minDistance = distance
          closestPoint = point
          seriesIndex = idx
        }
      })
    })

    // Add annotation if a close point was found and it's within a reasonable distance
    if (closestPoint && minDistance < 30) {
      const newAnnotation: Annotation = {
        id: `annotation-${Date.now()}`,
        x: xScale(closestPoint.wavelength) + margin.left,
        y: yScale(closestPoint.value) + margin.top,
        labelX: xScale(closestPoint.wavelength) + margin.left + 10,
        labelY: yScale(closestPoint.value) + margin.top - 10,
        wavelength: closestPoint.wavelength,
        value: closestPoint.value
      }

      setAnnotations([...annotations, newAnnotation])
    }
  }

  const handleAnnotationDragStart = (id: string) => {
    setDraggedAnnotation(id)
  }

  const handleAnnotationDrag = (event: React.MouseEvent<SVGGElement>) => {
    if (!draggedAnnotation) return

    const [mouseX, mouseY] = d3.pointer(event)

    setAnnotations(
      annotations.map((anno) =>
        anno.id === draggedAnnotation ? { ...anno, labelX: mouseX, labelY: mouseY } : anno
      )
    )
  }

  const handleAnnotationDragEnd = () => {
    setDraggedAnnotation(null)
  }

  const handleAnnotationDelete = (id: string) => {
    setAnnotations(annotations.filter((anno) => anno.id !== id))
  }

  useEffect(() => {
    if (!chartRef.current) return

    const svg = d3.select(chartRef.current)
    svg.selectAll('*').remove()

    const margin = { top: 20, right: 30, bottom: 40, left: 50 }
    const width = chartRef.current.clientWidth - margin.left - margin.right
    const height = chartRef.current.clientHeight - margin.top - margin.bottom

    const g = svg.append('g').attr('transform', `translate(${margin.left},${margin.top})`)

    // 计算 X 轴范围
    let xMin = Infinity
    let xMax = -Infinity

    // 计算 Y 轴范围
    let yMin = Infinity
    let yMax = -Infinity

    // 遍历所有可见的数据系列，找出 X 和 Y 的最小值和最大值
    chartData.forEach((series) => {
      if (!series.visible || !series.data.length) return

      series.data.forEach((point) => {
        xMin = Math.min(xMin, point.wavelength)
        xMax = Math.max(xMax, point.wavelength)
        yMin = Math.min(yMin, point.value)
        yMax = Math.max(yMax, point.value)
      })
    })

    // 如果没有有效数据，使用默认范围
    if (xMin === Infinity || xMax === -Infinity) {
      xMin = 1
      xMax = 6
    }

    if (yMin === Infinity || yMax === -Infinity) {
      yMin = 2
      yMax = 6
    }

    // 为坐标轴添加一些边距
    const xPadding = (xMax - xMin) * 0.05
    const yPadding = (yMax - yMin) * 0.05

    // X scale
    const xScale = d3
      .scaleLinear()
      .domain([xMin - xPadding, xMax + xPadding])
      .range([0, width])

    // Y scale
    const yScale = d3
      .scaleLinear()
      .domain([yMin - yPadding, yMax + yPadding])
      .range([height, 0])

    // X axis
    g.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .append('text')
      .attr('fill', '#000')
      .attr('x', width / 2)
      .attr('y', 35)
      .attr('text-anchor', 'middle')
      .text('波长 (nm)')

    // Y axis
    g.append('g')
      .call(d3.axisLeft(yScale))
      .append('text')
      .attr('fill', '#000')
      .attr('transform', 'rotate(-90)')
      .attr('y', -30)
      .attr('x', -height / 2)
      .attr('text-anchor', 'middle')
      .text(mode === 'T' ? '透过率 (%)' : mode === 'R' ? '反射率 (%)' : '吸光度')

    // Grid lines
    g.append('g')
      .attr('class', 'grid')
      .call(
        d3
          .axisLeft(yScale)
          .tickSize(-width)
          .tickFormat(() => '')
      )
      .style('stroke-opacity', 0.1)

    g.append('g')
      .attr('class', 'grid')
      .attr('transform', `translate(0,${height})`)
      .call(
        d3
          .axisBottom(xScale)
          .tickSize(-height)
          .tickFormat(() => '')
      )
      .style('stroke-opacity', 0.1)

    // Draw lines for each visible series
    chartData.forEach((series) => {
      if (!series.visible) return

      // Line generator
      const line = d3
        .line<SpectralDataPoint>()
        .x((d) => xScale(d.wavelength))
        .y((d) => yScale(d.value))

      // Draw line
      g.append('path')
        .datum(series.data)
        .attr('fill', 'none')
        .attr('stroke', series.color)
        .attr('stroke-width', 1.5)
        .attr('d', line)

      // Draw points
      g.selectAll(`.point-${series.id}`)
        .data(series.data)
        .enter()
        .append('circle')
        .attr('class', `point-${series.id}`)
        .attr('cx', (d) => xScale(d.wavelength))
        .attr('cy', (d) => yScale(d.value))
        .attr('r', 3)
        .attr('fill', series.color)
    })

    // Draw annotations
    const annotationGroup = svg.append('g').attr('class', 'annotations')

    annotations.forEach((anno) => {
      // Draw point marker
      annotationGroup
        .append('circle')
        .attr('cx', anno.x)
        .attr('cy', anno.y)
        .attr('r', 5)
        .attr('fill', 'red')
        .attr('stroke', 'white')
        .attr('stroke-width', 2)

      // Draw connecting line
      annotationGroup
        .append('line')
        .attr('x1', anno.x)
        .attr('y1', anno.y)
        .attr('x2', anno.labelX)
        .attr('y2', anno.labelY)
        .attr('stroke', 'gray')
        .attr('stroke-width', 1)
        .attr('stroke-dasharray', '3,3')

      // Draw label background
      const labelText = `X: ${anno.wavelength.toFixed(4)}, Y: ${anno.value.toFixed(4)}`
      const labelWidth = labelText.length * 6 + 10
      const labelHeight = 20

      annotationGroup
        .append('rect')
        .attr('x', anno.labelX)
        .attr('y', anno.labelY - 15)
        .attr('width', labelWidth)
        .attr('height', labelHeight)
        .attr('fill', 'white')
        .attr('stroke', 'gray')
        .attr('stroke-width', 1)
        .attr('rx', 3)
        .attr('ry', 3)

      // Draw label text
      annotationGroup
        .append('text')
        .attr('x', anno.labelX + 5)
        .attr('y', anno.labelY)
        .attr('text-anchor', 'start')
        .attr('font-size', '12px')
        .text(labelText)

      // Draw delete button
      annotationGroup
        .append('circle')
        .attr('cx', anno.labelX + labelWidth - 5)
        .attr('cy', anno.labelY - 15 + labelHeight / 2)
        .attr('r', 6)
        .attr('fill', 'red')
        .attr('cursor', 'pointer')
        .on('click', () => handleAnnotationDelete(anno.id))

      annotationGroup
        .append('text')
        .attr('x', anno.labelX + labelWidth - 5)
        .attr('y', anno.labelY - 15 + labelHeight / 2 + 4)
        .attr('text-anchor', 'middle')
        .attr('font-size', '10px')
        .attr('fill', 'white')
        .attr('cursor', 'pointer')
        .text('×')
        .on('click', () => handleAnnotationDelete(anno.id))

      // Make label draggable
      const labelGroup = annotationGroup
        .append('g')
        .attr('cursor', 'move')
        .on('mousedown', () => handleAnnotationDragStart(anno.id))
        .on('mousemove', handleAnnotationDrag)
        .on('mouseup', handleAnnotationDragEnd)
        .on('mouseleave', handleAnnotationDragEnd)

      labelGroup
        .append('rect')
        .attr('x', anno.labelX)
        .attr('y', anno.labelY - 15)
        .attr('width', labelWidth - 15)
        .attr('height', labelHeight)
        .attr('fill', 'transparent')
    })
  }, [mode, chartData, annotations])

  return (
    <div className="w-full h-full">
      <svg ref={chartRef} width="100%" height="100%" onClick={handleChartClick} />
    </div>
  )
}

export default SpectralChart
