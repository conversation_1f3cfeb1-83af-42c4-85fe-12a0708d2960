// @ts-nocheck
import { createContext, useContext, useState, useEffect, ReactNode, useCallback, JSX } from 'react'
import {
  checkActivation,
  getMachineId,
  validateActivationCode,
  saveActivationInfo
} from './activationUtils'

interface ActivationContextType {
  isActivated: boolean
  isLoading: boolean
  activationType: 'trial' | 'permanent' | null
  expiryDate: Date | null
  machineId: string
  activationError: string | null
  activateWithCode: (code: string) => Promise<boolean>
  checkActivationStatus: () => Promise<void>
}

const ActivationContext = createContext<ActivationContextType | undefined>(undefined)

export function useActivation(): ActivationContextType {
  const context = useContext(ActivationContext)
  if (context === undefined) {
    throw new Error('useActivation must be used within an ActivationProvider')
  }
  return context as ActivationContextType
}

interface ActivationProviderProps {
  children: ReactNode
}

export function ActivationProvider({ children }: ActivationProviderProps): JSX.Element {
  const [isActivated, setIsActivated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [activationType, setActivationType] = useState<'trial' | 'permanent' | null>(null)
  const [expiryDate, setExpiryDate] = useState<Date | null>(null)
  const [machineId, setMachineId] = useState<string>('')
  const [activationError, setActivationError] = useState<string | null>(null)

  // 计算剩余天数
  const getRemainingDays = useCallback((): number | null => {
    if (!expiryDate) return null

    const now = new Date()
    const diffTime = expiryDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays > 0 ? diffDays : 0
  }, [expiryDate])

  // 向主进程发送激活状态
  useEffect(() => {
    if (!isLoading) {
      window.electron.ipcRenderer.send('update-activation-status', {
        isLoading,
        isActivated,
        activationType,
        remainingDays: getRemainingDays()
      })
    }
  }, [isLoading, isActivated, activationType, getRemainingDays])

  // 初始化时获取机器码
  useEffect(() => {
    async function initialize(): Promise<void> {
      try {
        const id = await getMachineId()
        setMachineId(id)
        await checkActivationStatus()
      } catch (error) {
        console.error('初始化激活状态失败:', error)
        setActivationError('初始化激活状态失败')
      } finally {
        setIsLoading(false)
      }
    }

    initialize()
  }, [])

  // 检查激活状态
  const checkActivationStatus = useCallback(async (): Promise<void> => {
    setIsLoading(true)
    try {
      const result = await checkActivation()

      if (import.meta.env.VITE_ISADMIN === '1') {
        setIsActivated(true)
        setActivationType('permanent')
      } else {
        setIsActivated(result.activated)
        setActivationType(result.type || null)
        setExpiryDate(result.expiryDate || null)
        setActivationError(null)
      }
    } catch (error) {
      console.error('检查激活状态失败:', error)
      setActivationError('检查激活状态失败')
      setIsActivated(false)
      setActivationType(null)
      setExpiryDate(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 使用激活码激活
  const activateWithCode = async (code: string): Promise<boolean> => {
    setIsLoading(true)
    setActivationError(null)

    try {
      if (!code.trim()) {
        setActivationError('请输入有效的激活码')
        return false
      }

      const id = await getMachineId()
      const validationResult = validateActivationCode(code, id)

      if (!validationResult.valid) {
        if (validationResult.expired) {
          setActivationError('激活码已过期')
        } else {
          setActivationError('无效的激活码')
        }
        return false
      }

      // 保存激活信息
      saveActivationInfo(code, id)

      // 更新状态
      setIsActivated(true)
      setActivationType(validationResult.type || null)
      setExpiryDate(validationResult.expiryDate || null)

      return true
    } catch (error) {
      console.error('激活失败:', error)
      setActivationError('激活过程中发生错误')
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // 定期检查激活状态
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null
    // 每小时检查一次 (3600000毫秒)
    const checkInterval = 3600000

    if (!isLoading && isActivated && activationType === 'trial') {
      intervalId = setInterval(() => {
        console.log('定期检查激活状态...')
        checkActivationStatus()
      }, checkInterval)
    }

    // 清理定时器：组件卸载时 或 激活状态变为永久版时
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [isLoading, isActivated, activationType, checkActivationStatus])

  const value = {
    isActivated,
    isLoading,
    activationType,
    expiryDate,
    machineId,
    activationError,
    activateWithCode,
    checkActivationStatus
  }

  return <ActivationContext.Provider value={value}>{children}</ActivationContext.Provider>
}
