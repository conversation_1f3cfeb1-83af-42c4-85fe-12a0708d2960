import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// 存储事件监听器的引用，以便后续移除


// 设置菜单事件监听
const setupMenuListeners = (callback) => {
  // 移除旧的监听器（如果存在）
  // if (fileOpenedListener) ipcRenderer.removeListener('file-opened', fileOpenedListener)
  // if (openActivationPageListener) ipcRenderer.removeListener('open-activation-page', openActivationPageListener)
  // if (toggleThemeListener) ipcRenderer.removeListener('toggle-theme', toggleThemeListener)
  
  // 创建新的监听器并保存引用
  const fileOpenedListener = (_, filePath) => {
    callback('file-opened', filePath)
  }
  
  const  openActivationPageListener = () => {
    callback('open-activation-page')
  }
  const  openActivationPage2Listener = () => {
    callback('open-activation-page2')
  }
  const  toggleThemeListener = () => {
    callback('toggle-theme')
  }
  
  // 添加新的监听器
  ipcRenderer.on('file-opened', fileOpenedListener)
  ipcRenderer.on('open-activation-page', openActivationPageListener)
  ipcRenderer.on('open-activation-page2', openActivationPage2Listener)
  ipcRenderer.on('toggle-theme', toggleThemeListener)
}

// Custom APIs for renderer
const api = {
  // 打印api
  printView: (html) => ipcRenderer.invoke('print-view', html),
  // 激活相关API
  activation: {
    // 获取机器码
    getMachineId: () => ipcRenderer.invoke('get-machine-id'),
    // 更新激活状态到主进程
    updateActivationStatus: (status) => ipcRenderer.send('update-activation-status', status)
  },
  // 文件操作相关API
  fileOperations: {
    // 打开文件对话框
    openFileDialog: (options) => ipcRenderer.invoke('dialog:openFile', options),
    // 读取文件内容
    readFile: (filePath) => ipcRenderer.invoke('fs:readFile', filePath),
    // 监听菜单事件
    onMenuEvent: (callback) => setupMenuListeners(callback)
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
