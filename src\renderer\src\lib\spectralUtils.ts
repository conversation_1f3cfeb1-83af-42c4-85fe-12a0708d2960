/**
 * Spectral data utilities for conversion between T%, R%, and Abs
 */

export interface SpectralDataPoint {
  wavelength: number
  value: number
}

export interface SpectralSeries {
  id: string
  name: string
  mode: 'T' | 'R' | 'Abs'
  color: string
  visible: boolean
  data: SpectralDataPoint[]
}

/**
 * Convert T% or R% value to Abs
 * Formula: Abs = -log10(Y/100)
 * Example: If T% is 10%, Abs = 1.0000
 */
export function convertToAbs(value: number): number {
  // Value is in percentage (0-100), convert to decimal (0-1) first

  return -Math.log10(value)
}

/**
 * Convert Abs value to T% or R%
 * Formula: Y = 10^(-Abs) * 100
 * Example: If Abs is 2.0000, T% or R% = 1%
 */
export function convertFromAbs(absValue: number): number {
  return 10 ** -absValue
}

/**
 * Convert a series of data points from one mode to another
 */
export function convertSeries(
  data: SpectralDataPoint[],
  fromMode: 'T' | 'R' | 'Abs',
  toMode: 'T' | 'R' | 'Abs'
): SpectralDataPoint[] {
  // If the modes are the same, return the original data
  if (fromMode === toMode) {
    return [...data]
  }

  // Convert from T% or R% to Abs
  if ((fromMode === 'T' || fromMode === 'R') && toMode === 'Abs') {
    return data.map((point) => ({
      wavelength: point.wavelength,
      value: convertToAbs(point.value)
    }))
  }

  // Convert from Abs to T% or R%
  if (fromMode === 'Abs' && (toMode === 'T' || toMode === 'R')) {
    return data.map((point) => ({
      wavelength: point.wavelength,
      value: convertFromAbs(point.value)
    }))
  }

  // T% to R% or R% to T% (no conversion needed for values)
  return [...data]
}

/**
 * Find the maximum value in a range of wavelengths
 */
export function findMaxInRange(
  data: SpectralDataPoint[],
  startWavelength: number,
  endWavelength: number
): { value: number; wavelength: number } | null {
  const pointsInRange = data.filter(
    (point) => point.wavelength >= startWavelength && point.wavelength <= endWavelength
  )

  if (pointsInRange.length === 0) {
    return null
  }

  const maxPoint = pointsInRange.reduce(
    (max, point) => (point.value > max.value ? point : max),
    pointsInRange[0]
  )

  return { value: maxPoint.value, wavelength: maxPoint.wavelength }
}

/**
 * Find the minimum value in a range of wavelengths
 */
export function findMinInRange(
  data: SpectralDataPoint[],
  startWavelength: number,
  endWavelength: number
): { value: number; wavelength: number } | null {
  const pointsInRange = data.filter(
    (point) => point.wavelength >= startWavelength && point.wavelength <= endWavelength
  )

  if (pointsInRange.length === 0) {
    return null
  }

  const minPoint = pointsInRange.reduce(
    (min, point) => (point.value < min.value ? point : min),
    pointsInRange[0]
  )

  return { value: minPoint.value, wavelength: minPoint.wavelength }
}

/**
 * Calculate the average value in a range of wavelengths
 */
export function calculateAverageInRange(
  data: SpectralDataPoint[],
  startWavelength: number,
  endWavelength: number
): number | null {
  const pointsInRange = data.filter(
    (point) => point.wavelength >= startWavelength && point.wavelength <= endWavelength
  )

  if (pointsInRange.length === 0) {
    return null
  }

  const sum = pointsInRange.reduce((acc, point) => acc + point.value, 0)
  return sum / pointsInRange.length
}

/**
 * Find the wavelength where the value is equal to a target value
 * Uses linear interpolation between data points
 */
export function findWavelengthAtValue(
  data: SpectralDataPoint[],
  targetValue: number,
  startSearch: number,
  direction: 'left' | 'right'
): number | null {
  // 确保数据不为空
  if (data.length === 0) {
    return null
  }

  // 按波长排序
  const sortedData = [...data].sort((a, b) => a.wavelength - b.wavelength)

  // 如果没有找到确切的起点，使用最接近的点
  let startIndex = -1

  if (direction === 'left') {
    // 找到小于或等于起始波长的最大索引
    for (let i = 0; i < sortedData.length; i++) {
      if (sortedData[i].wavelength <= startSearch) {
        startIndex = i
      } else {
        break
      }
    }

    // 如果没有找到小于起始波长的点，使用第一个点
    if (startIndex === -1 && sortedData.length > 0) {
      startIndex = 0
    }
  } else {
    // direction === 'right'
    // 找到大于或等于起始波长的最小索引
    for (let i = 0; i < sortedData.length; i++) {
      if (sortedData[i].wavelength >= startSearch) {
        startIndex = i
        break
      }
    }

    // 如果没有找到大于起始波长的点，使用最后一个点
    if (startIndex === -1 && sortedData.length > 0) {
      startIndex = sortedData.length - 1
    }
  }

  // 如果仍然没有找到合适的起点，返回 null
  if (startIndex === -1) {
    return null
  }

  // 准备搜索数据
  const searchData =
    direction === 'left'
      ? sortedData.slice(0, startIndex + 1).reverse() // 左侧搜索，逆序
      : sortedData.slice(startIndex) // 右侧搜索，正序

  // 找到两个相邻点，其值包含目标值
  for (let i = 0; i < searchData.length - 1; i++) {
    const point1 = searchData[i]
    const point2 = searchData[i + 1]

    // 检查目标值是否在这两个点之间
    if (
      (point1.value <= targetValue && point2.value >= targetValue) ||
      (point1.value >= targetValue && point2.value <= targetValue)
    ) {
      // 执行线性插值
      const ratio = Math.abs((targetValue - point1.value) / (point2.value - point1.value))
      const wavelength = point1.wavelength + ratio * (point2.wavelength - point1.wavelength)

      return wavelength
    }
  }

  // 如果没有找到包含目标值的两个点，返回 null
  console.log(
    `未找到半高点波长，方向: ${direction}, 目标值: ${targetValue}, 起始波长: ${startSearch}`
  )
  return null
}

/**
 * Calculate the FWHM (Full Width at Half Maximum) for a peak
 */
export function calculateFWHM(
  data: SpectralDataPoint[],
  peakWavelength: number,
  peakValue: number,
  startWavelength: number, // 添加波长范围参数
  endWavelength: number // 添加波长范围参数
): {
  fwhm: number | null
  leftWavelength: number | null
  rightWavelength: number | null
  leftWavelength20: number | null
  rightWavelength20: number | null
  leftWavelength10: number | null
  rightWavelength10: number | null
} {
  if (data.length === 0 || peakValue <= 0) {
    console.log('数据为空或峰值不正确')
    return {
      fwhm: null,
      leftWavelength: null,
      rightWavelength: null,
      leftWavelength20: null,
      rightWavelength20: null,
      leftWavelength10: null,
      rightWavelength10: null
    }
  }

  const halfMaxValue = peakValue / 2
  const twentyPercentValue = peakValue * 0.2
  const tenPercentValue = peakValue * 0.1
  console.log(
    `计算FWHM: 峰值波长=${peakWavelength}, 峰值=${peakValue}, 半高值=${halfMaxValue}, 20%值=${twentyPercentValue}, 10%值=${tenPercentValue}, 波长范围=${startWavelength}-${endWavelength}`
  )

  // 确保数据按波长排序，并且只包含指定波长范围内的数据
  const sortedData = [...data]
    .filter((point) => point.wavelength >= startWavelength && point.wavelength <= endWavelength)
    .sort((a, b) => a.wavelength - b.wavelength)

  if (sortedData.length === 0) {
    console.log('指定波长范围内没有数据点')
    return {
      fwhm: null,
      leftWavelength: null,
      rightWavelength: null,
      leftWavelength20: null,
      rightWavelength20: null,
      leftWavelength10: null,
      rightWavelength10: null
    }
  }

  // 找到峰值点在排序数据中的索引
  const peakIndex = sortedData.findIndex(
    (point) =>
      Math.abs(point.wavelength - peakWavelength) < 0.001 &&
      Math.abs(point.value - peakValue) < 0.001
  )

  if (peakIndex === -1) {
    console.log('未找到确切的峰值点，使用最接近的点')
    // 如果找不到确切的峰值点，使用最接近的点
    const closestToPeak = sortedData.reduce((closest, point) => {
      const currentDiff = Math.abs(point.wavelength - peakWavelength)
      const closestDiff = Math.abs(closest.wavelength - peakWavelength)
      return currentDiff < closestDiff ? point : closest
    }, sortedData[0])

    peakWavelength = closestToPeak.wavelength
    peakValue = closestToPeak.value
  }

  // 找到半高点波长，限制在指定的波长范围内
  const leftWavelength = findWavelengthAtValue(sortedData, halfMaxValue, peakWavelength, 'left')
  const rightWavelength = findWavelengthAtValue(sortedData, halfMaxValue, peakWavelength, 'right')

  console.log(`左半高点波长: ${leftWavelength}, 右半高点波长: ${rightWavelength}`)

  // 计算FWHM
  const fwhm =
    leftWavelength !== null && rightWavelength !== null ? rightWavelength - leftWavelength : null

  if (fwhm !== null) {
    console.log(`计算得到FWHM: ${fwhm}`)
  } else {
    console.log('无法计算FWHM，因为未找到一个或两个半高点')
  }

  // 找到20%和10%的波长
  const leftWavelength20 = findWavelengthAtValue(
    sortedData,
    twentyPercentValue,
    peakWavelength,
    'left'
  )
  const rightWavelength20 = findWavelengthAtValue(
    sortedData,
    twentyPercentValue,
    peakWavelength,
    'right'
  )
  const leftWavelength10 = findWavelengthAtValue(
    sortedData,
    tenPercentValue,
    peakWavelength,
    'left'
  )
  const rightWavelength10 = findWavelengthAtValue(
    sortedData,
    tenPercentValue,
    peakWavelength,
    'right'
  )

  return {
    fwhm,
    leftWavelength,
    rightWavelength,
    leftWavelength20,
    rightWavelength20,
    leftWavelength10,
    rightWavelength10
  }
}
