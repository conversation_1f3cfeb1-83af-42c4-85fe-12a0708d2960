import React, { useState, useEffect } from 'react'
import { Checkbox } from './ui/checkbox'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { Input } from './ui/input'
import { useChart } from '../contexts/ChartContext'
import { Button } from './ui/button'

// 表格类型
export type TableType = 'feature1' | 'feature2'

// 表格属性接口
interface DataTableProps {
  type: TableType
}

// 功能一表格组件
const Feature1Table: React.FC = () => {
  const { state, dispatch } = useChart()
  const { feature1Data } = state

  const toggleRowSelection = (id: number): void => {
    dispatch({ type: 'TOGGLE_FEATURE1_SELECTION', payload: id })
  }

  const handleInputChange = (
    id: number,
    field: 'startWavelength' | 'endWavelength',
    value: string
  ): void => {
    dispatch({
      type: 'UPDATE_FEATURE1_ROW',
      payload: { id, field, value }
    })
  }
  
  const calculateValues = (): void => {
    dispatch({ type: 'CALCULATE_ALL_FEATURE1_VALUES' })
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="text-center w-1/10">序号</TableHead>
          <TableHead className="text-center w-1/7">波长起点</TableHead>
          <TableHead className="text-center w-1/7">波长终点</TableHead>
          <TableHead className="text-center w-1/7 ">最大值</TableHead>
          <TableHead className="text-center w-1/7">最小值</TableHead>
          <TableHead className="text-center w-1/7">平均值</TableHead>
          <TableHead className="text-center w-1/7">打印勾选</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {feature1Data.map((row) => (
          <TableRow key={row.id}>
            <TableCell className="text-center">{row.id}</TableCell>
            <TableCell className="text-center">
              <Input
                className="h-8 text-center "
                placeholder="请输入波长起点"
                value={row.startWavelength}
                onChange={(e) => handleInputChange(row.id, 'startWavelength', e.target.value)}
                onBlur={calculateValues}
              />
            </TableCell>
            <TableCell className="text-center ">
              <Input
                placeholder="请输入波长起点"
                className="h-8 text-center "
                value={row.endWavelength}
                onChange={(e) => handleInputChange(row.id, 'endWavelength', e.target.value)}
                onBlur={calculateValues}
              />
            </TableCell>
            <TableCell className="text-center">{row.maxValue || 'NA'}</TableCell>
            <TableCell className="text-center">{row.minValue || 'NA'}</TableCell>
            <TableCell className="text-center">{row.avgValue || 'NA'}</TableCell>
            <TableCell className="text-center">
              <div className="flex justify-center">
                <Checkbox
                  checked={row.selected}
                  onCheckedChange={() => toggleRowSelection(row.id)}
                />
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

// 功能二表格组件
const Feature2Table: React.FC = () => {
  const { state, dispatch } = useChart()
  const { feature2Data, activeFeature2RowId } = state

  const toggleRowSelection = (id: number): void => {
    dispatch({ type: 'TOGGLE_FEATURE2_SELECTION', payload: id })
  }

  const handleInputChange = (
    id: number,
    field: keyof Omit<(typeof feature2Data)[0], 'id' | 'selected'>,
    value: string
  ): void => {
    // 设置当前活动行
    dispatch({ type: 'SET_ACTIVE_FEATURE2_ROW', payload: id })

    // 更新行数据
    dispatch({
      type: 'UPDATE_FEATURE2_ROW',
      payload: { id, field, value }
    })
  }

  const calculateValues = (id: number): void => {
    // 设置当前活动行
    dispatch({ type: 'SET_ACTIVE_FEATURE2_ROW', payload: id })

    // 计算值
    dispatch({ type: 'CALCULATE_FEATURE2_VALUES', payload: id })
  }

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="text-center ">序号</TableHead>
            <TableHead className="text-center ">波长起点</TableHead>
            <TableHead className="text-center">波长终点</TableHead>
            <TableHead className="text-center">中心波长</TableHead>
            <TableHead className="text-center">半高宽</TableHead>
            <TableHead className="text-center">最大透过率Tmax</TableHead>
            <TableHead className="text-center">50%Tmax波长1</TableHead>
            <TableHead className="text-center">50%Tmax波长2</TableHead>
            <TableHead className="text-center">20%Tmax波长1</TableHead>
            <TableHead className="text-center">20%Tmax波长2</TableHead>
            <TableHead className="text-center">10%Tmax波长1</TableHead>
            <TableHead className="text-center">10%Tmax波长2</TableHead>
            <TableHead className="text-center">打印勾选</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {feature2Data.map((row) => (
            <TableRow key={row.id} className={activeFeature2RowId === row.id ? 'bg-muted/50' : ''}>
              <TableCell className="text-center">{row.id}</TableCell>
              <TableCell className="text-center">
                <Input
                  className="h-8 text-center"
                  placeholder="请输入"
                  value={row.startWavelength}
                  onChange={(e) => handleInputChange(row.id, 'startWavelength', e.target.value)}
                  onBlur={() => calculateValues(row.id)}
                  onFocus={() => dispatch({ type: 'SET_ACTIVE_FEATURE2_ROW', payload: row.id })}
                />
              </TableCell>
              <TableCell className="text-center">
                <Input
                  className="h-8 text-center"
                  placeholder="请输入"
                  value={row.endWavelength}
                  onChange={(e) => handleInputChange(row.id, 'endWavelength', e.target.value)}
                  onBlur={() => calculateValues(row.id)}
                  onFocus={() => dispatch({ type: 'SET_ACTIVE_FEATURE2_ROW', payload: row.id })}
                />
              </TableCell>
              <TableCell className="text-center">{row.centerWavelength || 'NA'}</TableCell>
              <TableCell className="text-center">{row.halfWidth || 'NA'}</TableCell>
              <TableCell className="text-center">{row.maxTransmittance || 'NA'}</TableCell>
              <TableCell className="text-center">{row.wavelength1 || 'NA'}</TableCell>
              <TableCell className="text-center">{row.wavelength2 || 'NA'}</TableCell>
              <TableCell className="text-center">{row.wavelength20_1 || 'NA'}</TableCell>
              <TableCell className="text-center">{row.wavelength20_2 || 'NA'}</TableCell>
              <TableCell className="text-center">{row.wavelength10_1 || 'NA'}</TableCell>
              <TableCell className="text-center">{row.wavelength10_2 || 'NA'}</TableCell>
              <TableCell className="text-center">
                <div className="flex justify-center">
                  <Checkbox
                    checked={row.selected}
                    onCheckedChange={() => toggleRowSelection(row.id)}
                  />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

// 主表格组件，根据类型显示不同的表格
const DataTable: React.FC<DataTableProps> = ({ type }) => {
  return <>{type === 'feature1' ? <Feature1Table /> : <Feature2Table />}</>
}

export default DataTable
