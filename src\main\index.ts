import { app, shell, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { join, dirname } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import { setupActivationHandlers } from './activation'
import { setupMenu } from './menu'
import * as fs from 'fs'
import { fileURLToPath } from 'node:url'

// derive the current directory
const __dirname = dirname(fileURLToPath(import.meta.url))

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    show: false,
    autoHideMenuBar: false,
    icon: join(__dirname, '../resources/icon.png'),
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.mjs'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    // 允许打印相关的弹出窗口
    if (details.url === 'about:blank') {
      return { action: 'allow' }
    }
    // 其他URL使用外部浏览器打开
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// 设置文件操作相关的IPC处理程序
function setupFileHandlers(): void {
  // 打开文件对话框
  ipcMain.handle('dialog:openFile', async (_, options) => {
    const { canceled, filePaths } = await dialog.showOpenDialog(options)
    return { canceled, filePaths }
  })

  // 读取文件内容
  ipcMain.handle('fs:readFile', async (_, filePath) => {
    try {
      return fs.readFileSync(filePath)
    } catch (error) {
      console.error('读取文件失败:', error)
      throw error
    }
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set up the activation handlers
  setupActivationHandlers()

  // Set up file operation handlers
  setupFileHandlers()

  // Set up application menu
  setupMenu()

  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
