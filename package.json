{"name": "specAnalysis", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "type": "module", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "test": "jest", "build": " electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux", "electron:generate-icons": "electron-icon-builder --input=./resources/icon.png --output=build --flatten"}, "dependencies": {"@electron-toolkit/preload": "^2.0.0", "@electron-toolkit/utils": "^2.0.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@visactor/react-vchart": "^1.13.11", "@visactor/vchart": "^1.13.10", "antd": "^5.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "d3": "^7.8.5", "echarts": "^5.6.0", "electron-print-preview": "^2.2.0", "electron-updater": "^6.1.1", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "node-machine-id": "^1.1.12", "react-color-palette": "^7.3.0", "react-router-dom": "^7.6.1", "sonner": "^2.0.3", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@jest/globals": "30.0.0-beta.3", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/vite": "^4.1.7", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "jest": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.7", "ts-jest": "^29.3.4", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "vite": "^6.2.6", "vite-plugin-style-import": "^2.0.0"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "core-js", "electron", "esbuild"]}}