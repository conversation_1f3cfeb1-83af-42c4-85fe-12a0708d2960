import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Plus, Calculator, Activity } from 'lucide-react'
import DataTable, { TableType } from './DataTable'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from './ui/tabs'
import { useChart } from '../contexts/ChartContext'

const DataSection: React.FC = () => {
  const { state, dispatch } = useChart()
  const { feature1Data, feature2Data, activeTab } = state

  const handleAddRow = () => {
    // 最多添加5行数据
    if (activeTab === 'feature1') {
      if (feature1Data.length >= 5) {
        return
      }
      dispatch({ type: 'ADD_FEATURE1_ROW' })
    } else {
      if (feature2Data.length >= 5) {
        return
      }
      dispatch({ type: 'ADD_FEATURE2_ROW' })
    }
  }

  // 处理Tab切换，重新计算所有行的数据
  const handleTabChange = (value: string) => {
    const newTab = value as TableType
    dispatch({ type: 'SET_ACTIVE_TAB', payload: newTab })

    // 当切换到功能一表格时，重新计算所有行的数据
    if (newTab === 'feature1') {
      dispatch({ type: 'CALCULATE_ALL_FEATURE1_VALUES' })
    }
  }

  return (
    <Card>
      <CardHeader className="py-3 px-4 border-b">
        <div className="flex items-center justify-between">
          <Tabs defaultValue="feature1" className="w-full" onValueChange={handleTabChange}>
            <TabsList className="grid w-[400px] grid-cols-2">
              <TabsTrigger value="feature1">
                <Calculator className="h-4 w-4 mr-2" />
                功能一
              </TabsTrigger>
              <TabsTrigger value="feature2">
                <Activity className="h-4 w-4 mr-2" />
                功能二
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button size="sm" variant="outline" className="h-8" onClick={handleAddRow}>
            <Plus className="h-3.5 w-3.5 mr-1" />
            添加行
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0 h-[280px]">
        <DataTable type={activeTab} />
      </CardContent>
    </Card>
  )
}

export default DataSection
