import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { useActivation } from '../../lib/ActivationContext'
import { Loader2, ShieldCheck, AlertCircle } from 'lucide-react'
import { Label } from '../ui/label'
import { toast } from 'sonner'
import { useNavigate } from 'react-router-dom'

interface ActivationDialogProps {
  onClose?: () => void
}

export function ActivationDialog({ onClose }: ActivationDialogProps) {
  const [activationCode, setActivationCode] = useState('')
  const { isLoading, activateWithCode, machineId, activationError } = useActivation()
  const navigate = useNavigate()
  const handleActivate = async () => {
    const success = await activateWithCode(activationCode)
    console.log('success', success)
    if (success && onClose) {
      onClose()
      // 跳转到首页
      navigate('/')
    }
  }

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-md">
          <ShieldCheck className="h-5 w-5 text-primary" />
          软件激活
        </CardTitle>
        <CardDescription>请输入您的激活码以继续使用完整功能</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">机器码</label>
            <div className="flex">
              <Label className="flex-1 bg-gray-50 px-3 rounded-lg text-gray-600">
                {' '}
                {machineId}
              </Label>
              <Button
                variant="outline"
                size="sm"
                className="ml-2 whitespace-nowrap"
                onClick={() => {
                  navigator.clipboard.writeText(machineId)
                  toast.success('复制成功')
                }}
              >
                复制
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              请将此机器码提供给软件供应商以获取激活码
            </p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">激活码</label>
            <Input
              value={activationCode}
              onChange={(e) => setActivationCode(e.target.value)}
              placeholder="请输入您的激活码"
              className="font-mono"
            />
          </div>

          {activationError && (
            <div className="flex items-center gap-2 text-sm text-destructive bg-destructive/10 p-2 rounded-md">
              <AlertCircle className="h-4 w-4" />
              {activationError}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button onClick={handleActivate} disabled={isLoading || !activationCode.trim()} size="lg">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              激活中...
            </>
          ) : (
            '激活软件'
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
