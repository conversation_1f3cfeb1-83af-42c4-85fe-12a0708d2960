import React, { JSX, useEffect } from 'react'
import { useActivation } from '../../lib/ActivationContext'
import { useLocation, useNavigate } from 'react-router-dom'

interface ActivationCheckProps {
  children: React.ReactNode
}

export function ActivationCheck({ children }: ActivationCheckProps): JSX.Element {
  const { isActivated, isLoading } = useActivation()
  const location = useLocation()
  const navigate = useNavigate()
  const isActivationPage = location.pathname === '/activation'

  // 当激活状态加载完成且未激活时，如果不在激活页面，则重定向到激活页面
  useEffect(() => {
    if (!isLoading && !isActivated && !isActivationPage) {
      navigate('/activation')
    }
  }, [isLoading, isActivated, isActivationPage, navigate])

  // 如果正在加载激活状态，显示加载中
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">正在检查激活状态...</p>
        </div>
      </div>
    )
  }

  // 如果已激活或在激活页面，直接显示子组件
  if (isActivated || isActivationPage) {
    return <>{children}</>
  }

  return <></>
}
