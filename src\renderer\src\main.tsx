import { createRoot } from 'react-dom/client'
import { StrictMode } from 'react'
import App from './App'
import './assets/main.css'
import { ActivationProvider } from './lib/ActivationContext'
import { ThemeProvider } from './lib/ThemeContext'
import { RouterProvider, createHashRouter } from 'react-router-dom'
import { ActivationPage, ActivationStatus } from './components/activation'
import { Toaster } from './components/ui/sonner'

// 错误边界组件
const ErrorBoundary = ({ error }: { error: Error }) => (
  <div className="flex items-center justify-center h-screen">
    <div className="text-center">
      <h1 className="text-2xl font-bold mb-4">应用程序错误</h1>
      <p className="text-gray-600 mb-4">抱歉，应用程序遇到了一个错误。</p>
      <button 
        onClick={() => window.location.reload()} 
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        重新加载
      </button>
    </div>
  </div>
)

// 创建路由配置 - 使用 createHashRouter
const router = createHashRouter([
  {
    path: '/',
    element: <App />,
    errorElement: <ErrorBoundary error={new Error('页面加载失败')} />
  },
  {
    path: '/activation',
    element: <ActivationPage />,
    errorElement: <ErrorBoundary error={new Error('激活页面加载失败')} />
  },
  {
    path: '/activationStatus',
    element: <ActivationStatus />,
    errorElement: <ErrorBoundary error={new Error('激活状态页面加载失败')} />
  }
])

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ActivationProvider>
      <ThemeProvider>
        <Toaster position="top-center" />
        <RouterProvider router={router} />
      </ThemeProvider>
    </ActivationProvider>
  </StrictMode>
)
