import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

// 菜单事件处理组件
const MenuEventHandler = () => {
  const navigate = useNavigate()

  // useEffect(() => {
  //   // 设置菜单事件监听
  //   const handleMenuEvent = (event: string, data?: any) => {
  //     console.log('Menu event received:', event, data)

  //     // 处理不同的菜单事件
  //     switch (event) {
  //       case 'file-opened':
  //         // 处理文件打开事件
  //         console.log('File opened:', data)
  //         break

  //       case 'open-activation-page':
  //         // 导航到激活页面
  //         navigate('/activationStatus')
  //         break
  //       case 'open-activation-page2':
  //           // 导航到激活页面
  //           console.log('da')
  //         // navigate('/activationStatus')
  //           break
  //       default:
  //         console.log('Unhandled menu event:', event)
  //     }
  //   }

  //   // 注册菜单事件监听器
  //   window.api.fileOperations.onMenuEvent(handleMenuEvent)

  //   // 清理函数
  //   return () => {
  //     // 在实际应用中，如果有清理监听器的方法，应该在这里调用
  //   }
  // }, [navigate])

  // 这个组件不渲染任何UI
  return null
}

export default MenuEventHandler
