import {  Menu, <PERSON>rowserWindow, dialog,  ipcMain } from 'electron'

export function setupMenu(): void {
  const isMac = process.platform === 'darwin'

  const template = [
    // 文件菜单
    {
      label: '文件',
      submenu: [
        {
          label: '打开文件',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const mainWindow = BrowserWindow.getFocusedWindow()
            if (mainWindow) {
              const { canceled, filePaths } = await dialog.showOpenDialog({
                properties: ['openFile'],
                filters: [
                  { name: 'Excel 文件', extensions: ['xlsx', 'xls', 'csv'] },
                  { name: '所有文件', extensions: ['*'] }
                ]
              })
              if (!canceled && filePaths.length > 0) {
                mainWindow.webContents.send('file-opened', filePaths[0])
              }
            }
          }
        },
        { type: 'separator' },
        {
          label: '激活状态',
         
          click: () => {
            const mainWindow = BrowserWindow.getFocusedWindow()
            if (mainWindow) {
              mainWindow.webContents.send('open-activation-page')
            }
          }
        },
       ,
        
        { type: 'separator' },
        isMac ? { role: 'close' } : { role: 'quit', label: '退出' }
      ]
    },

    // 视图菜单
    {
      label: '视图',
      submenu: [
        { role: 'reload', label: '重新加载' },
        { role: 'forceReload', label: '强制重新加载' },

        { role: 'toggleDevTools', label: '切换开发者工具' },
        { type: 'separator' },
        { role: 'resetZoom', label: '重置缩放' },
        { role: 'zoomIn', label: '放大' },
        { role: 'zoomOut', label: '缩小' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: '全屏' },
        { type: 'separator' },
       
      ]
    },
    // 帮助菜单
    {
      role: 'help',
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: async () => {
            dialog.showMessageBox({
              title: '关于',
              message: 'SpecAnalysis - 光谱分析软件',
              detail: '版本: 1.0.0\n版权所有 © 2025',
              buttons: ['确定'],
              type: 'info'
            })
          }
        },
        {
          label: '联系我们',
          click: async () => {
            dialog.showMessageBox({
              title: '联系我们',
              message: '手机号:187****1233',
              detail: '微信: 187****1233',
              buttons: ['确定'],
              type: 'info'
            })
          }
        }
      ]
    }
  ]
  import.meta.env.VITE_ISADMIN === '1'? template[0].submenu.push({
    label: '生成激活码',
    enabled: true, // Placeholder, will update dynamically
    click: () => {
      const mainWindow = BrowserWindow.getFocusedWindow()
      if (mainWindow) {
        mainWindow.webContents.send('open-activation-page2')
      }
    }
  }):null
  const menu = Menu.buildFromTemplate(template as any)
  Menu.setApplicationMenu(menu)

  // IPC listener for activation status updates
  // ipcMain.on('update-activation-status', (_event, status) => {
  //   const applicationMenu = Menu.getApplicationMenu()
  //   if (applicationMenu) {
  //     const fileMenu = applicationMenu.items.find((item) => item.label === '文件')
  //     if (fileMenu && fileMenu.submenu) {
  //       const activationStatusItem = fileMenu.submenu.items.find(
  //         (item) => item.label === '激活状态'
  //       )
  //       if (activationStatusItem) {
  //         if (status.isLoading) {
  //           activationStatusItem.label = '激活状态: 检查中...'
  //           activationStatusItem.enabled = false
  //         } else if (status.isActivated) {
  //           if (status.activationType === 'permanent') {
  //             activationStatusItem.label = '激活状态: 永久授权'
  //             activationStatusItem.enabled = true
  //           } else {
  //             const remainingDays = status.remainingDays !== null ? status.remainingDays : 'N/A'
  //             activationStatusItem.label = `激活状态: 试用版 (剩余 ${remainingDays} 天)`
  //             activationStatusItem.enabled = true
  //           }
  //         } else {
  //           activationStatusItem.label = '激活状态: 未激活'
  //           activationStatusItem.enabled = false
  //         }
  //       }
  //     }
  //   }
  // })
}
