import React, { JSX, useEffect } from 'react'
import { Button } from './ui/button'
import { Moon, Sun } from 'lucide-react'
import { useTheme } from '../lib/ThemeContext'

interface ThemeToggleProps {
  className?: string
}

export function ThemeToggle({ className }: ThemeToggleProps): JSX.Element {
  const { theme, toggleTheme } = useTheme()

  return (
    <Button
      variant="outline"
      size="sm"
      className={className}
      onClick={toggleTheme}
      title={theme === 'light' ? '切换到暗黑模式' : '切换到亮色模式'}
    >
      {theme === 'light' ? (
        <Moon className="h-[1.2rem] w-[1.2rem]" />
      ) : (
        <Sun className="h-[1.2rem] w-[1.2rem]" />
      )}
      <span className="sr-only">{theme === 'light' ? '切换到暗黑模式' : '切换到亮色模式'}</span>
    </Button>
  )
}
